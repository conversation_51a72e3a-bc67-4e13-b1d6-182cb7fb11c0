{"name": "@ag-ui/core", "author": "<PERSON> <<EMAIL>>", "version": "0.0.28", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"rxjs": "7.8.1", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.8.2"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}}