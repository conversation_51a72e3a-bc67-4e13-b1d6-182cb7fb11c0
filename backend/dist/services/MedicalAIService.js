"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicalAIService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class MedicalAIService {
    client;
    baseURL;
    constructor() {
        this.baseURL = process.env['GEMMA_MEDICAL_URL'] || 'http://192.168.0.179:1234';
        this.client = axios_1.default.create({
            baseURL: this.baseURL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
    async analyzeSupplement(request) {
        try {
            const prompt = this.buildMedicalPrompt(request);
            const response = await this.client.post('/v1/chat/completions', {
                model: 'gemma3-4b-medical',
                messages: [
                    {
                        role: 'system',
                        content: this.getSystemPrompt(request.analysisType)
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 2000,
                stream: false
            });
            const aiResponse = response.data.choices[0].message.content;
            return this.parseAIResponse(aiResponse, request.analysisType);
        }
        catch (error) {
            logger_1.logger.error('Medical AI analysis failed:', error);
            throw new Error('Failed to analyze medical information');
        }
    }
    async analyzeInteractions(substances) {
        try {
            const prompt = this.buildInteractionPrompt(substances);
            const response = await this.client.post('/v1/chat/completions', {
                model: 'gemma3-4b-medical',
                messages: [
                    {
                        role: 'system',
                        content: this.getInteractionSystemPrompt()
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.2,
                max_tokens: 1500
            });
            const aiResponse = response.data.choices[0].message.content;
            return this.parseInteractions(aiResponse);
        }
        catch (error) {
            logger_1.logger.error('Interaction analysis failed:', error);
            throw new Error('Failed to analyze drug interactions');
        }
    }
    buildMedicalPrompt(request) {
        const basePrompt = `
Please analyze the following text about supplements/medical information using Chain-of-Thought reasoning:

TEXT TO ANALYZE:
${request.text}

ANALYSIS TYPE: ${request.analysisType}
${request.context ? `ADDITIONAL CONTEXT: ${request.context}` : ''}

Please provide your analysis following this Chain-of-Thought structure:

1. INITIAL ASSESSMENT:
   - What type of medical/supplement information is this?
   - What are the key entities mentioned?

2. DETAILED ANALYSIS:
   - Medical properties and mechanisms
   - Potential benefits and effects
   - Known side effects or risks
   - Dosage considerations

3. INTERACTION ANALYSIS:
   - Potential drug interactions
   - Contraindications
   - Special populations (pregnancy, elderly, etc.)

4. EVIDENCE EVALUATION:
   - Quality of available evidence
   - Clinical trial data if available
   - Regulatory status

5. FINAL RECOMMENDATIONS:
   - Safety profile
   - Usage recommendations
   - Monitoring requirements

Please format your response as structured JSON with clear reasoning steps.
`;
        return basePrompt;
    }
    getSystemPrompt(analysisType) {
        const baseSystem = `You are a medical AI assistant specialized in supplement and drug analysis. You have extensive knowledge of:
- Pharmacology and drug interactions
- Supplement science and nutrition
- Clinical research and evidence evaluation
- Safety profiles and contraindications
- Dosage recommendations and therapeutic ranges

Always use Chain-of-Thought reasoning and provide evidence-based analysis.`;
        const typeSpecific = {
            supplement: 'Focus on supplement efficacy, safety, and interactions.',
            interaction: 'Focus on drug-drug and drug-supplement interactions.',
            side_effects: 'Focus on adverse effects and safety profiles.',
            dosage: 'Focus on therapeutic dosing and safety margins.',
            contraindications: 'Focus on conditions and populations where use is not recommended.'
        };
        return `${baseSystem}\n\n${typeSpecific[analysisType] || typeSpecific.supplement}`;
    }
    buildInteractionPrompt(substances) {
        return `
Analyze potential interactions between these substances using Chain-of-Thought reasoning:

SUBSTANCES: ${substances.join(', ')}

Please provide:
1. Pairwise interaction analysis
2. Mechanism of interaction
3. Clinical significance
4. Severity rating (1-10)
5. Management recommendations

Format as structured JSON with interaction pairs.
`;
    }
    getInteractionSystemPrompt() {
        return `You are a clinical pharmacology expert specializing in drug and supplement interactions. 
Analyze interactions based on:
- Pharmacokinetic interactions (absorption, metabolism, excretion)
- Pharmacodynamic interactions (additive, synergistic, antagonistic effects)
- Clinical evidence and case reports
- Severity and clinical significance

Always provide evidence-based analysis with confidence levels.`;
    }
    parseAIResponse(response, _analysisType) {
        try {
            const parsed = JSON.parse(response);
            return {
                analysis: parsed.analysis || response,
                entities: parsed.entities || [],
                interactions: parsed.interactions || [],
                recommendations: parsed.recommendations || [],
                confidence: parsed.confidence || 0.8,
                reasoning: parsed.reasoning || []
            };
        }
        catch (error) {
            return {
                analysis: response,
                entities: this.extractEntities(response),
                interactions: [],
                recommendations: this.extractRecommendations(response),
                confidence: 0.7,
                reasoning: this.extractReasoning(response)
            };
        }
    }
    parseInteractions(response) {
        try {
            const parsed = JSON.parse(response);
            return parsed.interactions || [];
        }
        catch (error) {
            return [];
        }
    }
    extractEntities(_text) {
        const entities = [];
        return entities;
    }
    extractRecommendations(_text) {
        const recommendations = [];
        return recommendations;
    }
    extractReasoning(_text) {
        const reasoning = [];
        return reasoning;
    }
    async healthCheck() {
        try {
            const response = await this.client.get('/v1/models');
            return response.status === 200;
        }
        catch (error) {
            logger_1.logger.error('Gemma3-4B health check failed:', error);
            return false;
        }
    }
}
exports.MedicalAIService = MedicalAIService;
//# sourceMappingURL=MedicalAIService.js.map