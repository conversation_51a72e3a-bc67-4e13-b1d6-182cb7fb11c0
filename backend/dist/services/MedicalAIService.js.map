{"version": 3, "file": "MedicalAIService.js", "sourceRoot": "", "sources": ["../../src/services/MedicalAIService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAC7C,4CAAyC;AAgCzC,MAAa,gBAAgB;IACnB,MAAM,CAAgB;IACtB,OAAO,CAAS;IAExB;QACE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,2BAA2B,CAAC;QAC/E,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,OAA+B;QACrD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC9D,KAAK,EAAE,mBAAmB;gBAC1B,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC;qBACpD;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;gBACD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAC5D,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,UAAoB;QAC5C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAEvD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC9D,KAAK,EAAE,mBAAmB;gBAC1B,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,IAAI,CAAC,0BAA0B,EAAE;qBAC3C;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;gBACD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;YAC5D,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAE5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,OAA+B;QACxD,MAAM,UAAU,GAAG;;;;EAIrB,OAAO,CAAC,IAAI;;iBAEG,OAAO,CAAC,YAAY;EACnC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BhE,CAAC;QAEE,OAAO,UAAU,CAAC;IACpB,CAAC;IAKO,eAAe,CAAC,YAAoB;QAC1C,MAAM,UAAU,GAAG;;;;;;;2EAOoD,CAAC;QAExE,MAAM,YAAY,GAAG;YACnB,UAAU,EAAE,yDAAyD;YACrE,WAAW,EAAE,sDAAsD;YACnE,YAAY,EAAE,+CAA+C;YAC7D,MAAM,EAAE,iDAAiD;YACzD,iBAAiB,EAAE,mEAAmE;SACvF,CAAC;QAEF,OAAO,GAAG,UAAU,OAAO,YAAY,CAAC,YAAyC,CAAC,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;IAClH,CAAC;IAKO,sBAAsB,CAAC,UAAoB;QACjD,OAAO;;;cAGG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;CAUlC,CAAC;IACA,CAAC;IAKO,0BAA0B;QAChC,OAAO;;;;;;;+DAOoD,CAAC;IAC9D,CAAC;IAKO,eAAe,CAAC,QAAgB,EAAE,aAAqB;QAC7D,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,OAAO;gBACL,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,QAAQ;gBACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;gBAC/B,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;gBACvC,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,EAAE;gBAC7C,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,GAAG;gBACpC,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE;aAClC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO;gBACL,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;gBACxC,YAAY,EAAE,EAAE;gBAChB,eAAe,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC;gBACtD,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;aAC3C,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACpC,OAAO,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAKO,eAAe,CAAC,KAAa;QAEnC,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,sBAAsB,CAAC,KAAa;QAC1C,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,gBAAgB,CAAC,KAAa;QACpC,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACrD,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAzQD,4CAyQC"}