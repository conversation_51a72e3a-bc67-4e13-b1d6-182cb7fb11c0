interface AnalysisOptions {
    analysisType?: 'supplement' | 'ingredient' | 'interaction' | 'study' | 'general';
    model?: string;
    includeConfidence?: boolean;
}
interface ExtractionOptions {
    entityTypes?: string[];
    relationshipTypes?: string[];
    model?: string;
    confidence?: number;
}
interface RecommendationOptions {
    conditions?: string[];
    goals?: string[];
    currentSupplements?: string[];
    restrictions?: string[];
    maxRecommendations?: number;
}
interface InteractionOptions {
    includeFood?: boolean;
    includeMedications?: boolean;
}
interface SummaryOptions {
    summaryType?: 'brief' | 'detailed' | 'technical' | 'consumer';
    maxLength?: number;
    focusAreas?: string[];
}
interface QuestionOptions {
    questionType?: 'research' | 'safety' | 'efficacy' | 'dosage' | 'general';
    count?: number;
    difficulty?: 'basic' | 'intermediate' | 'advanced';
}
interface ValidationOptions {
    evidenceLevel?: 'low' | 'medium' | 'high';
}
interface ChatOptions {
    context?: any[];
    model?: string;
    temperature?: number;
}
export declare class AIService {
    private gemini;
    private openai;
    private lmStudio;
    constructor();
    analyzeText(text: string, options: AnalysisOptions): Promise<any>;
    extractEntitiesAndRelationships(text: string, options: ExtractionOptions): Promise<any>;
    generateRecommendations(options: RecommendationOptions): Promise<any>;
    checkInteractions(supplements: string[], options: InteractionOptions): Promise<any>;
    summarizeText(text: string, options: SummaryOptions): Promise<any>;
    generateQuestions(topic: string, options: QuestionOptions): Promise<any>;
    validateClaims(claims: string[], supplement: string, options: ValidationOptions): Promise<any>;
    chat(message: string, options: ChatOptions): Promise<any>;
    private chatWithLMStudio;
    getAvailableModels(): Promise<any>;
    getStats(): Promise<any>;
    findRelatedConcepts(concept: string, options: any): Promise<any[]>;
    private hashString;
    private buildAnalysisPrompt;
    private buildExtractionPrompt;
    private buildRecommendationPrompt;
    private buildInteractionPrompt;
    private buildSummaryPrompt;
    private buildQuestionPrompt;
    private buildValidationPrompt;
    private buildChatPrompt;
    private parseAnalysisResponse;
    private parseExtractionResponse;
    private parseRecommendationResponse;
    private parseInteractionResponse;
    private parseSummaryResponse;
    private parseQuestionResponse;
    private parseValidationResponse;
    private parseJsonResponse;
}
export {};
//# sourceMappingURL=AIService.d.ts.map