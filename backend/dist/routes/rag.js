"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ragRoutes = void 0;
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const express_validator_1 = require("express-validator");
const RAGService_1 = require("@/services/RAGService");
const router = (0, express_1.Router)();
exports.ragRoutes = router;
const ragService = new RAGService_1.RAGService();
const validateRequest = (req, _res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError(`Validation failed: ${errors.array().map(e => e.msg).join(', ')}`);
    }
    next();
};
router.post('/query', [
    (0, express_validator_1.body)('question').notEmpty().withMessage('Question is required'),
    (0, express_validator_1.body)('context').optional().isArray().withMessage('Context must be an array'),
    (0, express_validator_1.body)('maxResults').optional().isInt({ min: 1, max: 50 }).withMessage('maxResults must be between 1 and 50'),
    (0, express_validator_1.body)('similarityThreshold').optional().isFloat({ min: 0, max: 1 }).withMessage('similarityThreshold must be between 0 and 1'),
    (0, express_validator_1.body)('includeMetadata').optional().isBoolean().withMessage('includeMetadata must be a boolean'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { question, context = [], maxResults = 10, similarityThreshold = 0.7, includeMetadata = true, } = req.body;
    const options = {
        maxResults,
        similarityThreshold,
        includeMetadata,
        context,
    };
    const result = await ragService.queryKnowledgeBase(question, options);
    res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/embed', [
    (0, express_validator_1.body)('text').notEmpty().withMessage('Text is required'),
    (0, express_validator_1.body)('metadata').optional().isObject().withMessage('Metadata must be an object'),
    (0, express_validator_1.body)('className').optional().isString().withMessage('className must be a string'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { text, metadata = {}, className = 'Document', } = req.body;
    const embedding = await ragService.createEmbedding(text, metadata, className);
    res.status(201).json({
        success: true,
        data: embedding,
        message: 'Embedding created successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.post('/similar', [
    (0, express_validator_1.body)('text').optional().isString().withMessage('Text must be a string'),
    (0, express_validator_1.body)('vector').optional().isArray().withMessage('Vector must be an array'),
    (0, express_validator_1.body)('className').optional().isString().withMessage('className must be a string'),
    (0, express_validator_1.body)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
    (0, express_validator_1.body)('threshold').optional().isFloat({ min: 0, max: 1 }).withMessage('threshold must be between 0 and 1'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { text, vector, className = 'Document', limit = 10, threshold = 0.7, } = req.body;
    if (!text && !vector) {
        throw new errorHandler_1.ValidationError('Either text or vector is required');
    }
    const options = {
        className,
        limit,
        threshold,
    };
    const results = text
        ? await ragService.findSimilarByText(text, options)
        : await ragService.findSimilarByVector(vector, options);
    res.status(200).json({
        success: true,
        data: results,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/documents', [
    (0, express_validator_1.body)('content').notEmpty().withMessage('Content is required'),
    (0, express_validator_1.body)('title').optional().isString().withMessage('Title must be a string'),
    (0, express_validator_1.body)('source').optional().isString().withMessage('Source must be a string'),
    (0, express_validator_1.body)('metadata').optional().isObject().withMessage('Metadata must be an object'),
    (0, express_validator_1.body)('className').optional().isString().withMessage('className must be a string'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { content, title, source, metadata = {}, className = 'Document', } = req.body;
    const document = await ragService.addDocument({
        content,
        title,
        source,
        metadata,
        className,
    });
    res.status(201).json({
        success: true,
        data: document,
        message: 'Document added to knowledge base',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/documents/:id', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const document = await ragService.getDocument(id || '');
    res.status(200).json({
        success: true,
        data: document,
        timestamp: new Date().toISOString(),
    });
}));
router.put('/documents/:id', [
    (0, express_validator_1.body)('content').optional().isString().withMessage('Content must be a string'),
    (0, express_validator_1.body)('title').optional().isString().withMessage('Title must be a string'),
    (0, express_validator_1.body)('metadata').optional().isObject().withMessage('Metadata must be an object'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const updates = req.body;
    const document = await ragService.updateDocument(id || '', updates);
    res.status(200).json({
        success: true,
        data: document,
        message: 'Document updated successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.delete('/documents/:id', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    await ragService.deleteDocument(id || '');
    res.status(200).json({
        success: true,
        message: 'Document deleted successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/documents', [
    (0, express_validator_1.query)('q').optional().isString().withMessage('Query must be a string'),
    (0, express_validator_1.query)('className').optional().isString().withMessage('className must be a string'),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
    (0, express_validator_1.query)('offset').optional().isInt({ min: 0 }).withMessage('offset must be non-negative'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { q: query, className, limit = 20, offset = 0, } = req.query;
    const options = {
        className: className,
        limit: parseInt(limit),
        offset: parseInt(offset),
    };
    const results = query
        ? await ragService.searchDocuments(query, options)
        : await ragService.listDocuments(options);
    res.status(200).json({
        success: true,
        data: results,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/answer', [
    (0, express_validator_1.body)('question').notEmpty().withMessage('Question is required'),
    (0, express_validator_1.body)('context').optional().isArray().withMessage('Context must be an array'),
    (0, express_validator_1.body)('useGraph').optional().isBoolean().withMessage('useGraph must be a boolean'),
    (0, express_validator_1.body)('maxContextLength').optional().isInt({ min: 100, max: 10000 }).withMessage('maxContextLength must be between 100 and 10000'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { question, context = [], useGraph = true, maxContextLength = 4000, } = req.body;
    const options = {
        context,
        useGraph,
        maxContextLength,
    };
    const answer = await ragService.generateAnswer(question, options);
    res.status(200).json({
        success: true,
        data: answer,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/stats', (0, errorHandler_1.catchAsync)(async (_req, res) => {
    const stats = await ragService.getStats();
    res.status(200).json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/reindex', [
    (0, express_validator_1.body)('className').optional().isString().withMessage('className must be a string'),
    (0, express_validator_1.body)('batchSize').optional().isInt({ min: 1, max: 1000 }).withMessage('batchSize must be between 1 and 1000'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { className, batchSize = 100, } = req.body;
    const result = await ragService.reindexDocuments({
        className,
        batchSize,
    });
    res.status(200).json({
        success: true,
        data: result,
        message: 'Reindexing completed successfully',
        timestamp: new Date().toISOString(),
    });
}));
//# sourceMappingURL=rag.js.map