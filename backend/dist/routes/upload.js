"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadRoutes = void 0;
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const errorHandler_1 = require("@/middleware/errorHandler");
const environment_1 = require("@/config/environment");
const UploadService_1 = require("@/services/UploadService");
const router = (0, express_1.Router)();
exports.uploadRoutes = router;
const uploadService = new UploadService_1.UploadService();
const storage = multer_1.default.diskStorage({
    destination: async (_req, _file, cb) => {
        const uploadDir = environment_1.config.upload.uploadDir;
        try {
            await promises_1.default.mkdir(uploadDir, { recursive: true });
            cb(null, uploadDir);
        }
        catch (error) {
            cb(error, uploadDir);
        }
    },
    filename: (_req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path_1.default.extname(file.originalname);
        const name = path_1.default.basename(file.originalname, ext);
        cb(null, `${name}-${uniqueSuffix}${ext}`);
    },
});
const fileFilter = (_req, file, cb) => {
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/csv',
        'application/json',
        'application/xml',
        'text/xml',
    ];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb(new errorHandler_1.ValidationError(`File type ${file.mimetype} is not allowed`));
    }
};
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: environment_1.config.upload.maxFileSize,
        files: 10,
    },
});
router.post('/file', upload.single('file'), (0, errorHandler_1.catchAsync)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_1.ValidationError('No file uploaded');
    }
    const { extractText = 'true', addToKnowledgeBase = 'false', source, metadata, } = req.body;
    const options = {
        extractText: extractText === 'true',
        addToKnowledgeBase: addToKnowledgeBase === 'true',
        source,
        metadata: metadata ? JSON.parse(metadata) : {},
    };
    const result = await uploadService.processFile(req.file, options);
    res.status(201).json({
        success: true,
        data: result,
        message: 'File uploaded and processed successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.post('/files', upload.array('files', 10), (0, errorHandler_1.catchAsync)(async (req, res) => {
    const files = req.files;
    if (!files || files.length === 0) {
        throw new errorHandler_1.ValidationError('No files uploaded');
    }
    const { extractText = 'true', addToKnowledgeBase = 'false', source, metadata, } = req.body;
    const options = {
        extractText: extractText === 'true',
        addToKnowledgeBase: addToKnowledgeBase === 'true',
        source,
        metadata: metadata ? JSON.parse(metadata) : {},
    };
    const results = await uploadService.processFiles(files, options);
    res.status(201).json({
        success: true,
        data: results,
        message: `${files.length} files uploaded and processed successfully`,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/url', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { url, extractText = true, addToKnowledgeBase = false, source, metadata = {}, } = req.body;
    if (!url) {
        throw new errorHandler_1.ValidationError('URL is required');
    }
    const options = {
        extractText,
        addToKnowledgeBase,
        source,
        metadata,
    };
    const result = await uploadService.processUrl(url, options);
    res.status(201).json({
        success: true,
        data: result,
        message: 'URL content processed successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/files/:id', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const file = await uploadService.getFile(id || '');
    res.status(200).json({
        success: true,
        data: file,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/files/:id/download', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    if (!id) {
        throw new errorHandler_1.ValidationError('File ID is required');
    }
    const file = await uploadService.getFile(id);
    if (!file.filePath || !await uploadService.fileExists(file.filePath)) {
        throw new errorHandler_1.ValidationError('File not found on disk');
    }
    res.download(file.filePath, file.originalName);
}));
router.get('/files/:id/content', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    if (!id) {
        throw new errorHandler_1.ValidationError('File ID is required');
    }
    const content = await uploadService.getFileContent(id);
    res.status(200).json({
        success: true,
        data: content,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/files', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { page = 1, limit = 20, type, source, search, } = req.query;
    const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        type: type,
        source: source,
        search: search,
    };
    const result = await uploadService.listFiles(options);
    res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
    });
}));
router.delete('/files/:id', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    if (!id) {
        throw new errorHandler_1.ValidationError('File ID is required');
    }
    await uploadService.deleteFile(id);
    res.status(200).json({
        success: true,
        message: 'File deleted successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.post('/batch', (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { fileIds, operation, options = {}, } = req.body;
    if (!fileIds || !Array.isArray(fileIds)) {
        throw new errorHandler_1.ValidationError('fileIds must be an array');
    }
    if (!operation) {
        throw new errorHandler_1.ValidationError('operation is required');
    }
    const result = await uploadService.batchProcess(fileIds, operation, options);
    res.status(200).json({
        success: true,
        data: result,
        message: `Batch operation ${operation} completed`,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/stats', (0, errorHandler_1.catchAsync)(async (_req, res) => {
    const stats = await uploadService.getStats();
    res.status(200).json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
    });
}));
router.use((error, _req, _res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            throw new errorHandler_1.ValidationError('File too large');
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            throw new errorHandler_1.ValidationError('Too many files');
        }
        if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            throw new errorHandler_1.ValidationError('Unexpected file field');
        }
    }
    next(error);
});
//# sourceMappingURL=upload.js.map