"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logBusinessEvent = exports.logSecurityEvent = exports.logGraphOperation = exports.logAiOperation = exports.logApiRequest = exports.logDatabaseOperation = exports.logPerformance = exports.logDebug = exports.logWarn = exports.logError = exports.logInfo = exports.loggerStream = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const winston_daily_rotate_file_1 = __importDefault(require("winston-daily-rotate-file"));
const environment_1 = require("@/config/environment");
const levels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
};
const colors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'white',
};
winston_1.default.addColors(colors);
const format = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.colorize({ all: true }), winston_1.default.format.printf((info) => `${info['timestamp']} ${info.level}: ${info.message}${info['stack'] ? '\n' + info['stack'] : ''}`));
const transports = [
    new winston_1.default.transports.Console({
        format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple()),
    }),
];
if (environment_1.config.nodeEnv !== 'test') {
    transports.push(new winston_daily_rotate_file_1.default({
        filename: 'logs/application-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '14d',
        format: winston_1.default.format.combine(winston_1.default.format.uncolorize(), winston_1.default.format.json()),
    }));
    transports.push(new winston_daily_rotate_file_1.default({
        level: 'error',
        filename: 'logs/error-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '30d',
        format: winston_1.default.format.combine(winston_1.default.format.uncolorize(), winston_1.default.format.json()),
    }));
}
exports.logger = winston_1.default.createLogger({
    level: environment_1.config.logging.level,
    levels,
    format,
    transports,
    exitOnError: false,
});
exports.loggerStream = {
    write: (message) => {
        exports.logger.http(message.trim());
    },
};
const logInfo = (message, meta) => {
    exports.logger.info(message, meta);
};
exports.logInfo = logInfo;
const logError = (message, error, meta) => {
    if (error instanceof Error) {
        exports.logger.error(message, { error: error.message, stack: error.stack, ...meta });
    }
    else {
        exports.logger.error(message, { error, ...meta });
    }
};
exports.logError = logError;
const logWarn = (message, meta) => {
    exports.logger.warn(message, meta);
};
exports.logWarn = logWarn;
const logDebug = (message, meta) => {
    exports.logger.debug(message, meta);
};
exports.logDebug = logDebug;
const logPerformance = (operation, startTime, meta) => {
    const duration = Date.now() - startTime;
    exports.logger.info(`Performance: ${operation} completed in ${duration}ms`, { duration, operation, ...meta });
};
exports.logPerformance = logPerformance;
const logDatabaseOperation = (operation, collection, duration, meta) => {
    const message = `Database: ${operation} on ${collection}${duration ? ` (${duration}ms)` : ''}`;
    exports.logger.debug(message, { operation, collection, duration, ...meta });
};
exports.logDatabaseOperation = logDatabaseOperation;
const logApiRequest = (method, url, statusCode, duration, meta) => {
    const level = statusCode >= 400 ? 'warn' : 'info';
    const message = `API: ${method} ${url} - ${statusCode} (${duration}ms)`;
    exports.logger.log(level, message, { method, url, statusCode, duration, ...meta });
};
exports.logApiRequest = logApiRequest;
const logAiOperation = (operation, model, tokens, duration, meta) => {
    const message = `AI: ${operation} with ${model}${tokens ? ` (${tokens} tokens)` : ''}${duration ? ` (${duration}ms)` : ''}`;
    exports.logger.info(message, { operation, model, tokens, duration, ...meta });
};
exports.logAiOperation = logAiOperation;
const logGraphOperation = (operation, nodeCount, relationshipCount, duration, meta) => {
    const message = `Graph: ${operation}${nodeCount ? ` (${nodeCount} nodes)` : ''}${relationshipCount ? ` (${relationshipCount} relationships)` : ''}${duration ? ` (${duration}ms)` : ''}`;
    exports.logger.info(message, { operation, nodeCount, relationshipCount, duration, ...meta });
};
exports.logGraphOperation = logGraphOperation;
const logSecurityEvent = (event, severity, meta) => {
    const level = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
    const message = `Security: ${event} (${severity})`;
    exports.logger.log(level, message, { event, severity, ...meta });
};
exports.logSecurityEvent = logSecurityEvent;
const logBusinessEvent = (event, entityType, entityId, meta) => {
    const message = `Business: ${event}${entityType ? ` for ${entityType}` : ''}${entityId ? ` (${entityId})` : ''}`;
    exports.logger.info(message, { event, entityType, entityId, ...meta });
};
exports.logBusinessEvent = logBusinessEvent;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map