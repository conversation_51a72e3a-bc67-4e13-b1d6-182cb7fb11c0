import { GoogleGenerativeAI } from '@google/generative-ai';
import OpenAI from 'openai';
import { config } from '@/config/environment';
import { cacheGet, cacheSet } from '@/config/redis';
import { logAiOperation, logError } from '@/utils/logger';
import { ExternalServiceError } from '@/middleware/errorHandler';
import crypto from 'crypto';

interface AnalysisOptions {
  analysisType?: 'supplement' | 'ingredient' | 'interaction' | 'study' | 'general';
  model?: string;
  includeConfidence?: boolean;
}

interface ExtractionOptions {
  entityTypes?: string[];
  relationshipTypes?: string[];
  model?: string;
  confidence?: number;
}

interface RecommendationOptions {
  conditions?: string[];
  goals?: string[];
  currentSupplements?: string[];
  restrictions?: string[];
  maxRecommendations?: number;
}

interface InteractionOptions {
  includeFood?: boolean;
  includeMedications?: boolean;
}

interface SummaryOptions {
  summaryType?: 'brief' | 'detailed' | 'technical' | 'consumer';
  maxLength?: number;
  focusAreas?: string[];
}

interface QuestionOptions {
  questionType?: 'research' | 'safety' | 'efficacy' | 'dosage' | 'general';
  count?: number;
  difficulty?: 'basic' | 'intermediate' | 'advanced';
}

interface ValidationOptions {
  evidenceLevel?: 'low' | 'medium' | 'high';
}

interface ChatOptions {
  context?: any[];
  model?: string;
  temperature?: number;
}

export class AIService {
  private gemini: GoogleGenerativeAI;
  private openai: OpenAI | null = null;
  private lmStudio: OpenAI | null = null;

  constructor() {
    this.gemini = new GoogleGenerativeAI(config.ai.geminiApiKey);

    if (config.ai.openaiApiKey) {
      this.openai = new OpenAI({
        apiKey: config.ai.openaiApiKey,
      });
    }

    // Initialize LM Studio as OpenAI-compatible client
    if (config.ai.lmStudioUrl) {
      this.lmStudio = new OpenAI({
        apiKey: config.ai.lmStudioApiKey || 'lm-studio',
        baseURL: config.ai.lmStudioUrl + '/v1',
      });
    }
  }

  async analyzeText(text: string, options: AnalysisOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `ai:analyze:${this.hashString(text)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const model = this.gemini.getGenerativeModel({ 
        model: options.model || 'gemini-pro' 
      });

      const prompt = this.buildAnalysisPrompt(text, options);
      const result = await model.generateContent(prompt);
      const response = result.response;
      const analysisText = response.text();

      // Parse the AI response
      const analysis = this.parseAnalysisResponse(analysisText, options);

      // Cache the result
      await cacheSet(cacheKey, analysis, config.cache.ttl);

      const duration = Date.now() - startTime;
      logAiOperation('Analyze text', options.model || 'gemini-pro', undefined, duration);

      return analysis;
    } catch (error) {
      logError('Failed to analyze text', error, { options });
      throw new ExternalServiceError('AI analysis failed');
    }
  }

  async extractEntitiesAndRelationships(text: string, options: ExtractionOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `ai:extract:${this.hashString(text)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const model = this.gemini.getGenerativeModel({ 
        model: options.model || 'gemini-pro' 
      });

      const prompt = this.buildExtractionPrompt(text, options);
      const result = await model.generateContent(prompt);
      const response = result.response;
      const extractionText = response.text();

      // Parse the AI response
      const extraction = this.parseExtractionResponse(extractionText, options);

      // Cache the result
      await cacheSet(cacheKey, extraction, config.cache.ttl);

      const duration = Date.now() - startTime;
      logAiOperation('Extract entities', options.model || 'gemini-pro', undefined, duration);

      return extraction;
    } catch (error) {
      logError('Failed to extract entities and relationships', error, { options });
      throw new ExternalServiceError('Entity extraction failed');
    }
  }

  async generateRecommendations(options: RecommendationOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `ai:recommend:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
      const prompt = this.buildRecommendationPrompt(options);
      
      const result = await model.generateContent(prompt);
      const response = result.response;
      const recommendationText = response.text();

      // Parse the AI response
      const recommendations = this.parseRecommendationResponse(recommendationText, options);

      // Cache the result for shorter time (recommendations can change)
      await cacheSet(cacheKey, recommendations, 1800); // 30 minutes

      const duration = Date.now() - startTime;
      logAiOperation('Generate recommendations', 'gemini-pro', undefined, duration);

      return recommendations;
    } catch (error) {
      logError('Failed to generate recommendations', error, { options });
      throw new ExternalServiceError('Recommendation generation failed');
    }
  }

  async checkInteractions(supplements: string[], options: InteractionOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `ai:interactions:${JSON.stringify(supplements)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
      const prompt = this.buildInteractionPrompt(supplements, options);
      
      const result = await model.generateContent(prompt);
      const response = result.response;
      const interactionText = response.text();

      // Parse the AI response
      const interactions = this.parseInteractionResponse(interactionText, supplements, options);

      // Cache the result
      await cacheSet(cacheKey, interactions, config.cache.ttl);

      const duration = Date.now() - startTime;
      logAiOperation('Check interactions', 'gemini-pro', undefined, duration);

      return interactions;
    } catch (error) {
      logError('Failed to check interactions', error, { supplements, options });
      throw new ExternalServiceError('Interaction check failed');
    }
  }

  async summarizeText(text: string, options: SummaryOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `ai:summarize:${this.hashString(text)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
      const prompt = this.buildSummaryPrompt(text, options);
      
      const result = await model.generateContent(prompt);
      const response = result.response;
      const summaryText = response.text();

      // Parse the AI response
      const summary = this.parseSummaryResponse(summaryText, options);

      // Cache the result
      await cacheSet(cacheKey, summary, config.cache.ttl);

      const duration = Date.now() - startTime;
      logAiOperation('Summarize text', 'gemini-pro', undefined, duration);

      return summary;
    } catch (error) {
      logError('Failed to summarize text', error, { options });
      throw new ExternalServiceError('Text summarization failed');
    }
  }

  async generateQuestions(topic: string, options: QuestionOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `ai:questions:${topic}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
      const prompt = this.buildQuestionPrompt(topic, options);
      
      const result = await model.generateContent(prompt);
      const response = result.response;
      const questionText = response.text();

      // Parse the AI response
      const questions = this.parseQuestionResponse(questionText, options);

      // Cache the result
      await cacheSet(cacheKey, questions, config.cache.ttl);

      const duration = Date.now() - startTime;
      logAiOperation('Generate questions', 'gemini-pro', undefined, duration);

      return questions;
    } catch (error) {
      logError('Failed to generate questions', error, { topic, options });
      throw new ExternalServiceError('Question generation failed');
    }
  }

  async validateClaims(claims: string[], supplement: string, options: ValidationOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `ai:validate:${supplement}:${JSON.stringify(claims)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
      const prompt = this.buildValidationPrompt(claims, supplement, options);
      
      const result = await model.generateContent(prompt);
      const response = result.response;
      const validationText = response.text();

      // Parse the AI response
      const validation = this.parseValidationResponse(validationText, claims, options);

      // Cache the result
      await cacheSet(cacheKey, validation, config.cache.ttl);

      const duration = Date.now() - startTime;
      logAiOperation('Validate claims', 'gemini-pro', undefined, duration);

      return validation;
    } catch (error) {
      logError('Failed to validate claims', error, { claims, supplement, options });
      throw new ExternalServiceError('Claim validation failed');
    }
  }

  async chat(message: string, options: ChatOptions): Promise<any> {
    const startTime = Date.now();
    const modelName = options.model || 'gemini-pro';

    try {
      // Check if this is an LM Studio model
      if (this.lmStudio && (modelName.includes('llama') || modelName.includes('gemma') || modelName.includes('qwen') || modelName === 'local-model')) {
        return await this.chatWithLMStudio(message, options);
      }

      // Use Gemini for default models
      const model = this.gemini.getGenerativeModel({
        model: modelName,
        generationConfig: {
          temperature: options.temperature || config.ai.temperature,
          maxOutputTokens: config.ai.maxTokens,
        },
      });

      const prompt = this.buildChatPrompt(message, options);
      const result = await model.generateContent(prompt);
      const response = result.response;
      const responseText = response.text();

      const chatResponse = {
        message: responseText,
        model: modelName,
        timestamp: new Date().toISOString(),
        context: options.context,
      };

      const duration = Date.now() - startTime;
      logAiOperation('Chat', modelName, undefined, duration);

      return chatResponse;
    } catch (error) {
      logError('Failed to process chat message', error, { message, options });
      throw new ExternalServiceError('Chat processing failed');
    }
  }

  private async chatWithLMStudio(message: string, options: ChatOptions): Promise<any> {
    if (!this.lmStudio) {
      throw new ExternalServiceError('LM Studio not configured');
    }

    const startTime = Date.now();
    const modelName = options.model || 'local-model';

    try {
      const messages = [
        {
          role: 'system' as const,
          content: 'You are a knowledgeable assistant specializing in supplements, nutrition, and health. Always include appropriate medical disclaimers when discussing health topics.',
        },
        ...(options.context || []),
        {
          role: 'user' as const,
          content: message,
        },
      ];

      const completion = await this.lmStudio.chat.completions.create({
        model: modelName,
        messages,
        temperature: options.temperature || config.ai.temperature,
        max_tokens: config.ai.maxTokens,
      });

      const responseText = completion.choices[0]?.message?.content || '';

      const chatResponse = {
        message: responseText,
        model: modelName,
        timestamp: new Date().toISOString(),
        context: options.context,
        provider: 'lm-studio',
      };

      const duration = Date.now() - startTime;
      logAiOperation('Chat', modelName, undefined, duration);

      return chatResponse;
    } catch (error) {
      logError('Failed to process chat message with LM Studio', error, { message, options });
      throw new ExternalServiceError('LM Studio chat processing failed');
    }
  }

  async getAvailableModels(): Promise<any> {
    const models: any = {
      gemini: [
        'gemini-pro',
        'gemini-pro-vision',
      ],
      openai: this.openai ? [
        'gpt-4',
        'gpt-4-turbo-preview',
        'gpt-3.5-turbo',
      ] : [],
      lmStudio: this.lmStudio ? [
        'local-model',
        'llama-3.2',
        'gemma-2',
        'qwen-2.5',
      ] : [],
      default: config.ai.defaultModel,
    };

    // Try to get actual models from LM Studio if available
    if (this.lmStudio) {
      try {
        const lmModels = await this.lmStudio.models.list();
        models.lmStudio = lmModels.data.map(model => model.id);
      } catch (error) {
        logError('Failed to fetch LM Studio models', error);
        // Keep default models if API call fails
      }
    }

    return models;
  }

  async getStats(): Promise<any> {
    // This would typically come from a database or monitoring system
    return {
      totalRequests: 0, // Placeholder
      averageResponseTime: 0, // Placeholder
      modelsUsed: await this.getAvailableModels(),
      cacheHitRate: 0, // Placeholder
    };
  }

  // Helper method to find related concepts (used by GraphService)
  async findRelatedConcepts(concept: string, options: any): Promise<any[]> {
    const startTime = Date.now();

    try {
      const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
      const prompt = `
        Find ${options.limit || 10} concepts related to "${concept}" in the context of supplements and health.
        
        For each related concept, provide:
        1. Name
        2. Type (Supplement, Ingredient, Effect, Condition, etc.)
        3. Description (brief)
        4. Relationship type to "${concept}"
        5. Confidence score (0-1)
        
        Return as JSON array with this structure:
        [
          {
            "name": "concept name",
            "type": "concept type",
            "description": "brief description",
            "relationshipType": "relationship type",
            "confidence": 0.8
          }
        ]
      `;
      
      const result = await model.generateContent(prompt);
      const response = result.response;
      const responseText = response.text();

      // Parse JSON response
      const concepts = this.parseJsonResponse(responseText);

      const duration = Date.now() - startTime;
      logAiOperation('Find related concepts', 'gemini-pro', undefined, duration);

      return concepts;
    } catch (error) {
      logError('Failed to find related concepts', error, { concept, options });
      return [];
    }
  }

  // Private helper methods
  private hashString(str: string): string {
    return crypto.createHash('md5').update(str).digest('hex');
  }

  private buildAnalysisPrompt(text: string, options: AnalysisOptions): string {
    const analysisType = options.analysisType || 'general';

    return `
      Analyze the following text in the context of ${analysisType} information:

      "${text}"

      Please provide a comprehensive analysis including:
      1. Key findings and insights
      2. Important entities mentioned (supplements, ingredients, effects, etc.)
      3. Safety considerations or warnings
      4. Scientific evidence level (if applicable)
      5. Practical implications
      ${options.includeConfidence ? '6. Confidence score for each finding (0-1)' : ''}

      Format your response as structured JSON with clear sections.
    `;
  }

  private buildExtractionPrompt(text: string, options: ExtractionOptions): string {
    const entityTypes = options.entityTypes || ['Supplement', 'Ingredient', 'Effect', 'Study', 'Interaction'];
    const relationshipTypes = options.relationshipTypes || ['CONTAINS', 'CAUSES', 'INTERACTS_WITH', 'STUDIED_IN'];

    return `
      Extract entities and relationships from the following text:

      "${text}"

      Entity types to look for: ${entityTypes.join(', ')}
      Relationship types to identify: ${relationshipTypes.join(', ')}

      Return a JSON object with this structure:
      {
        "entities": [
          {
            "name": "entity name",
            "type": "entity type",
            "description": "brief description",
            "confidence": 0.8
          }
        ],
        "relationships": [
          {
            "source": "source entity name",
            "target": "target entity name",
            "type": "relationship type",
            "confidence": 0.7
          }
        ]
      }

      Only include entities and relationships with confidence >= ${options.confidence || 0.7}.
    `;
  }

  private buildRecommendationPrompt(options: RecommendationOptions): string {
    return `
      Generate supplement recommendations based on the following criteria:

      Health conditions: ${options.conditions?.join(', ') || 'None specified'}
      Health goals: ${options.goals?.join(', ') || 'None specified'}
      Current supplements: ${options.currentSupplements?.join(', ') || 'None'}
      Restrictions/allergies: ${options.restrictions?.join(', ') || 'None'}

      Please provide up to ${options.maxRecommendations || 5} recommendations.

      For each recommendation, include:
      1. Supplement name
      2. Reason for recommendation
      3. Suggested dosage
      4. Potential benefits
      5. Safety considerations
      6. Evidence level
      7. Confidence score (0-1)

      Return as JSON array with structured data.

      IMPORTANT: Include appropriate medical disclaimers and advise consulting healthcare providers.
    `;
  }

  private buildInteractionPrompt(supplements: string[], options: InteractionOptions): string {
    return `
      Check for potential interactions between these supplements:
      ${supplements.join(', ')}

      ${options.includeFood ? 'Also check for food interactions.' : ''}
      ${options.includeMedications ? 'Also check for common medication interactions.' : ''}

      For each potential interaction, provide:
      1. Supplements involved
      2. Interaction type (synergistic, antagonistic, dangerous, etc.)
      3. Severity level (low, medium, high, critical)
      4. Description of the interaction
      5. Recommendations
      6. Evidence level

      Return as JSON array with structured interaction data.

      IMPORTANT: Include medical disclaimers and emphasize consulting healthcare providers.
    `;
  }

  private buildSummaryPrompt(text: string, options: SummaryOptions): string {
    const summaryType = options.summaryType || 'brief';
    const maxLength = options.maxLength || 500;
    const focusAreas = options.focusAreas || [];

    return `
      Create a ${summaryType} summary of the following text (max ${maxLength} words):

      "${text}"

      ${focusAreas.length > 0 ? `Focus particularly on: ${focusAreas.join(', ')}` : ''}

      Summary style guidelines:
      - Brief: Key points only, very concise
      - Detailed: Comprehensive but accessible
      - Technical: Include scientific details and terminology
      - Consumer: Easy to understand, practical focus

      Return as JSON with:
      {
        "summary": "summary text",
        "keyPoints": ["point 1", "point 2", ...],
        "wordCount": number,
        "summaryType": "${summaryType}"
      }
    `;
  }

  private buildQuestionPrompt(topic: string, options: QuestionOptions): string {
    return `
      Generate ${options.count || 5} ${options.difficulty || 'intermediate'} level questions about: ${topic}

      Question type: ${options.questionType || 'general'}

      Question categories:
      - Research: Questions about studies and evidence
      - Safety: Questions about side effects and contraindications
      - Efficacy: Questions about effectiveness and benefits
      - Dosage: Questions about proper usage and amounts
      - General: Broad questions about the topic

      For each question, provide:
      1. The question text
      2. Question category
      3. Difficulty level
      4. Expected answer type (factual, analytical, opinion, etc.)

      Return as JSON array with structured question data.
    `;
  }

  private buildValidationPrompt(claims: string[], supplement: string, options: ValidationOptions): string {
    return `
      Validate the following claims about ${supplement}:

      Claims to validate:
      ${claims.map((claim, i) => `${i + 1}. ${claim}`).join('\n')}

      Evidence level required: ${options.evidenceLevel || 'medium'}

      For each claim, provide:
      1. Claim text
      2. Validation status (supported, partially supported, not supported, insufficient evidence)
      3. Evidence level (high, medium, low, none)
      4. Supporting evidence summary
      5. Contradicting evidence (if any)
      6. Confidence score (0-1)
      7. Recommendations

      Return as JSON array with structured validation data.

      IMPORTANT: Be objective and evidence-based. Include appropriate disclaimers.
    `;
  }

  private buildChatPrompt(message: string, options: ChatOptions): string {
    const contextStr = options.context && options.context.length > 0
      ? `\n\nContext from previous conversation:\n${options.context.map(c => `${c.role}: ${c.content}`).join('\n')}`
      : '';

    return `
      You are a knowledgeable assistant specializing in supplements, nutrition, and health.

      User message: ${message}${contextStr}

      Please provide a helpful, accurate, and informative response. Always include appropriate medical disclaimers when discussing health topics.
    `;
  }

  private parseAnalysisResponse(text: string, options: AnalysisOptions): any {
    try {
      return this.parseJsonResponse(text);
    } catch (error) {
      // Fallback to structured text parsing
      return {
        analysis: text,
        type: options.analysisType,
        timestamp: new Date().toISOString(),
        includeConfidence: options.includeConfidence,
      };
    }
  }

  private parseExtractionResponse(text: string, _options: ExtractionOptions): any {
    try {
      return this.parseJsonResponse(text);
    } catch (error) {
      // Fallback response
      return {
        entities: [],
        relationships: [],
        error: 'Failed to parse extraction response',
        rawResponse: text,
      };
    }
  }

  private parseRecommendationResponse(text: string, _options: RecommendationOptions): any {
    try {
      return this.parseJsonResponse(text);
    } catch (error) {
      // Fallback response
      return {
        recommendations: [],
        disclaimer: 'Please consult with a healthcare provider before starting any supplement regimen.',
        error: 'Failed to parse recommendation response',
        rawResponse: text,
      };
    }
  }

  private parseInteractionResponse(text: string, supplements: string[], _options: InteractionOptions): any {
    try {
      return this.parseJsonResponse(text);
    } catch (error) {
      // Fallback response
      return {
        interactions: [],
        supplements,
        disclaimer: 'Please consult with a healthcare provider about potential supplement interactions.',
        error: 'Failed to parse interaction response',
        rawResponse: text,
      };
    }
  }

  private parseSummaryResponse(text: string, options: SummaryOptions): any {
    try {
      return this.parseJsonResponse(text);
    } catch (error) {
      // Fallback response
      return {
        summary: text,
        summaryType: options.summaryType,
        wordCount: text.split(' ').length,
        keyPoints: [],
        error: 'Failed to parse summary response',
      };
    }
  }

  private parseQuestionResponse(text: string, options: QuestionOptions): any {
    try {
      return this.parseJsonResponse(text);
    } catch (error) {
      // Fallback response
      return {
        questions: [],
        topic: 'Unknown',
        questionType: options.questionType,
        difficulty: options.difficulty,
        error: 'Failed to parse question response',
        rawResponse: text,
      };
    }
  }

  private parseValidationResponse(text: string, claims: string[], options: ValidationOptions): any {
    try {
      return this.parseJsonResponse(text);
    } catch (error) {
      // Fallback response
      return {
        validations: [],
        claims,
        evidenceLevel: options.evidenceLevel,
        disclaimer: 'This validation is for informational purposes only. Consult healthcare providers for medical advice.',
        error: 'Failed to parse validation response',
        rawResponse: text,
      };
    }
  }

  private parseJsonResponse(text: string): any {
    // Try to extract JSON from the response
    const jsonMatch = text.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[0]);
      } catch (error) {
        throw new Error('Invalid JSON in response');
      }
    }

    // If no JSON found, try to parse the entire text
    try {
      return JSON.parse(text);
    } catch (error) {
      throw new Error('No valid JSON found in response');
    }
  }
}
