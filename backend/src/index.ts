import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { createServer } from 'http';

// Import configurations and middleware
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { requestLogger } from '@/middleware/requestLogger';

// Import database connections
import { connectNeo4j } from '@/config/neo4j';
import { connectWeaviate } from '@/config/weaviate';
import { connectRedis } from '@/config/redis';
import { connectMongoDB } from '@/config/mongodb';

// Import routes
import { graphRoutes } from '@/routes/graph';
import { ragRoutes } from '@/routes/rag';
import { aiRoutes } from '@/routes/ai';
import { healthRoutes } from '@/routes/health';
import { uploadRoutes } from '@/routes/upload';
import researchRoutes from '@/routes/research';
import aguiRoutes, { initializeAGUIWebSocket } from '@/routes/agui';

// Load environment variables
dotenv.config();

class Application {
  public app: express.Application;
  public server: any;
  private port: number;

  constructor() {
    this.app = express();
    this.port = config.port;
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.corsOrigins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: config.isDevelopment ? 1000 : 100, // Limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Body parsing middleware
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // Compression middleware
    this.app.use(compression());

    // Logging middleware
    if (config.isDevelopment) {
      this.app.use(morgan('dev'));
    } else {
      this.app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
    }

    // Custom request logging
    this.app.use(requestLogger);

    // Health check endpoint (before other routes)
    this.app.get('/health', (_req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.nodeEnv,
      });
    });
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use('/api/health', healthRoutes);
    this.app.use('/api/graph', graphRoutes);
    this.app.use('/api/rag', ragRoutes);
    this.app.use('/api/ai', aiRoutes);
    this.app.use('/api/upload', uploadRoutes);
    this.app.use('/api/research', researchRoutes);
    this.app.use('/agui', aguiRoutes);

    // API documentation
    this.app.get('/api', (_req, res) => {
      res.json({
        name: 'Suplementor Knowledge Graph API',
        version: '1.0.0',
        description: 'API for managing supplement knowledge graphs with AI integration',
        endpoints: {
          health: '/api/health',
          graph: '/api/graph',
          rag: '/api/rag',
          ai: '/api/ai',
          upload: '/api/upload',
          research: '/api/research',
          agui: '/agui',
        },
        websockets: {
          agui: '/agui/ws',
        },
        documentation: '/api/docs',
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  private async initializeDatabases(): Promise<void> {
    try {
      logger.info('Initializing database connections...');

      // Connect to Neo4j
      await connectNeo4j();
      logger.info('✅ Neo4j connected successfully');

      // Connect to Weaviate (optional - don't fail if unavailable)
      try {
        await connectWeaviate();
        logger.info('✅ Weaviate connected successfully');
      } catch (error) {
        logger.warn('⚠️ Weaviate connection failed, continuing without vector database');
      }

      // Connect to Redis
      await connectRedis();
      logger.info('✅ Redis connected successfully');

      // Connect to MongoDB
      await connectMongoDB();
      logger.info('✅ MongoDB connected successfully');

      logger.info('🚀 All databases connected successfully');
    } catch (error) {
      logger.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  public async start(): Promise<void> {
    try {
      // Initialize database connections
      await this.initializeDatabases();

      // Start HTTP server
      this.server = createServer(this.app);

      // Initialize AG-UI WebSocket server
      initializeAGUIWebSocket(this.server);

      this.server.listen(this.port, () => {
        logger.info(`🚀 Server running on port ${this.port}`);
        logger.info(`📊 Environment: ${config.nodeEnv}`);
        logger.info(`🔗 API URL: http://localhost:${this.port}/api`);
        logger.info(`🔌 AG-UI WebSocket: ws://localhost:${this.port}/agui/ws`);
        logger.info(`💚 Health check: http://localhost:${this.port}/health`);
      });

      // Graceful shutdown handling
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }

  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`📴 Received ${signal}. Starting graceful shutdown...`);
      
      if (this.server) {
        this.server.close(async () => {
          logger.info('🔌 HTTP server closed');
          
          try {
            // Close database connections
            // Note: Individual database disconnect methods will be implemented in their respective config files
            logger.info('🔌 Database connections closed');
            logger.info('✅ Graceful shutdown completed');
            process.exit(0);
          } catch (error) {
            logger.error('❌ Error during graceful shutdown:', error);
            process.exit(1);
          }
        });
      } else {
        process.exit(0);
      }
    };

    // Handle different termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('❌ Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
  }
}

// Create and start the application
const app = new Application();

// Start the server
app.start().catch((error) => {
  logger.error('❌ Failed to start application:', error);
  process.exit(1);
});

export default app;
