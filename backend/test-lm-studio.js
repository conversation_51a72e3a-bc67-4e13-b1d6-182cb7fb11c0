const https = require('https');
const http = require('http');
const { URL } = require('url');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.data) {
      req.write(options.data);
    }

    req.end();
  });
}

async function testLMStudio() {
  const lmStudioUrl = 'http://*************:1234';
  
  console.log('Testing LM Studio connection...');
  console.log('URL:', lmStudioUrl);
  
  try {
    // Test basic connectivity
    console.log('\n1. Testing basic connectivity...');
    const response = await makeRequest(lmStudioUrl);
    console.log('Status:', response.statusCode);
    console.log('Response preview:', response.data.substring(0, 200) + '...');

    // Test OpenAI-compatible endpoint
    console.log('\n2. Testing OpenAI-compatible endpoint...');
    const modelsResponse = await makeRequest(`${lmStudioUrl}/v1/models`, {
      headers: {
        'Authorization': 'Bearer lm-studio',
        'Content-Type': 'application/json'
      }
    });

    console.log('Models endpoint status:', modelsResponse.statusCode);

    if (modelsResponse.statusCode === 200) {
      const modelsData = JSON.parse(modelsResponse.data);
      console.log('Available models:', JSON.stringify(modelsData, null, 2));
    } else {
      console.log('Models endpoint error:', modelsResponse.data);
    }

    // Test chat completion
    console.log('\n3. Testing chat completion...');
    const chatResponse = await makeRequest(`${lmStudioUrl}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer lm-studio',
        'Content-Type': 'application/json'
      },
      data: JSON.stringify({
        model: 'local-model',
        messages: [
          {
            role: 'user',
            content: 'Hello, can you tell me about vitamin D?'
          }
        ],
        max_tokens: 100,
        temperature: 0.7
      })
    });

    console.log('Chat completion status:', chatResponse.statusCode);

    if (chatResponse.statusCode === 200) {
      const chatData = JSON.parse(chatResponse.data);
      console.log('Chat response:', JSON.stringify(chatData, null, 2));
    } else {
      console.log('Chat completion error:', chatResponse.data);
    }
    
  } catch (error) {
    console.error('Connection error:', error.message);
    console.error('This might indicate:');
    console.error('1. LM Studio is not running');
    console.error('2. LM Studio is not accessible at the specified IP/port');
    console.error('3. Network connectivity issues');
    console.error('4. Firewall blocking the connection');
  }
}

testLMStudio();
