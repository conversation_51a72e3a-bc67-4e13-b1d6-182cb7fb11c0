import React, { useState, useEffect, useRef } from 'react';
import Card from '@/components/atoms/Card';
import { <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import Button from '@/components/atoms/Button';
import Input from '@/components/atoms/Input';
import Badge from '@/components/atoms/Badge';
// import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '../ui/tabs';
import { Search, Brain, MessageCircle, Activity, Loader2 } from 'lucide-react';
import { SupplementGraph } from './SupplementGraph';
import { GemmaChat } from '../chat/GemmaChat';
import { useAGUIClient } from '../../hooks/useAGUIClient';
import { toast } from 'sonner';

interface ResearchState {
  isResearching: boolean;
  currentSupplement: string | null;
  progress: string[];
  graphData: any;
  insights: any;
}

export const SupplementResearchInterface: React.FC = () => {
  const [supplementName, setSupplementName] = useState('');
  const [researchDepth, setResearchDepth] = useState<'basic' | 'comprehensive'>('basic');
  const [researchState, setResearchState] = useState<ResearchState>({
    isResearching: false,
    currentSupplement: null,
    progress: [],
    graphData: null,
    insights: null
  });
  const [activeTab, setActiveTab] = useState('input');
  
  const progressRef = useRef<HTMLDivElement>(null);
  
  const { 
    isConnected, 
    sendMessage, 
    lastMessage,
    connectionStatus 
  } = useAGUIClient();

  // Handle incoming AG-UI messages
  useEffect(() => {
    if (!lastMessage) return;

    const { type, event, data } = lastMessage;

    switch (type) {
      case 'agent_event':
        handleAgentEvent(event);
        break;
      
      case 'graph_state':
        setResearchState(prev => ({
          ...prev,
          graphData: data
        }));
        break;
      
      case 'agent_completed':
        setResearchState(prev => ({
          ...prev,
          isResearching: false
        }));
        toast.success('Research completed successfully!');
        setActiveTab('graph');
        break;
      
      case 'agent_error':
        setResearchState(prev => ({
          ...prev,
          isResearching: false
        }));
        toast.error('Research failed. Please try again.');
        break;
    }
  }, [lastMessage]);

  const handleAgentEvent = (event: any) => {
    switch (event.type) {
      case 'RUN_STARTED':
        setResearchState(prev => ({
          ...prev,
          isResearching: true,
          progress: ['🔍 Starting research...']
        }));
        break;
      
      case 'TEXT_MESSAGE_CONTENT':
        setResearchState(prev => ({
          ...prev,
          progress: [...prev.progress, event.delta]
        }));
        scrollToBottom();
        break;
      
      case 'TOOL_CALL_START':
        const toolMessage = getToolMessage(event.toolCallName);
        setResearchState(prev => ({
          ...prev,
          progress: [...prev.progress, toolMessage]
        }));
        break;
      
      case 'STATE_DELTA':
        if (event.delta.supplement) {
          setResearchState(prev => ({
            ...prev,
            graphData: event.delta.supplement,
            insights: event.delta.supplement
          }));
        }
        break;
    }
  };

  const getToolMessage = (toolName: string): string => {
    const toolMessages: Record<string, string> = {
      'tavily_research': '🌐 Gathering research data from web sources...',
      'gemma_analysis': '🧠 Analyzing data with AI...',
      'update_knowledge_graph': '📊 Updating knowledge graph...'
    };
    return toolMessages[toolName] || `🔧 Running ${toolName}...`;
  };

  const scrollToBottom = () => {
    if (progressRef.current) {
      progressRef.current.scrollTop = progressRef.current.scrollHeight;
    }
  };

  const handleStartResearch = async () => {
    if (!supplementName.trim()) {
      toast.error('Please enter a supplement name');
      return;
    }

    if (!isConnected) {
      toast.error('Not connected to research service');
      return;
    }

    setResearchState({
      isResearching: true,
      currentSupplement: supplementName,
      progress: [],
      graphData: null,
      insights: null
    });

    setActiveTab('progress');

    sendMessage({
      type: 'start_supplement_research',
      payload: {
        supplementName: supplementName.trim(),
        researchDepth,
        includeInteractions: true
      }
    });
  };

  const handleQueryGraph = async (query: string) => {
    if (!isConnected) {
      toast.error('Not connected to research service');
      return;
    }

    sendMessage({
      type: 'natural_language_query',
      payload: {
        query,
        context: {
          currentSupplement: researchState.currentSupplement,
          graphData: researchState.graphData
        }
      }
    });
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Supplement Research & Knowledge Visualization
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Enter a supplement name to research its effects and visualize relationships with health domains
        </p>
        <div className="flex items-center justify-center gap-2">
          <Badge variant={isConnected ? 'primary' : 'error'}>
            {connectionStatus}
          </Badge>
        </div>
      </div>

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="input" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            Research Input
          </TabsTrigger>
          <TabsTrigger value="progress" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="graph" className="flex items-center gap-2">
            <Brain className="w-4 h-4" />
            Knowledge Graph
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4" />
            AI Chat
          </TabsTrigger>
        </TabsList>

        {/* Research Input Tab */}
        <TabsContent value="input" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Start Supplement Research</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Supplement Name</label>
                <Input
                  placeholder="Enter supplement name (e.g., Magnesium, Omega-3, Vitamin D)"
                  value={supplementName}
                  onChange={(e) => setSupplementName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleStartResearch()}
                  disabled={researchState.isResearching}
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Research Depth</label>
                <div className="flex gap-2">
                  <Button
                    variant={researchDepth === 'basic' ? 'primary' : 'outline'}
                    onClick={() => setResearchDepth('basic')}
                    disabled={researchState.isResearching}
                  >
                    Basic
                  </Button>
                  <Button
                    variant={researchDepth === 'comprehensive' ? 'primary' : 'outline'}
                    onClick={() => setResearchDepth('comprehensive')}
                    disabled={researchState.isResearching}
                  >
                    Comprehensive
                  </Button>
                </div>
              </div>

              <Button
                onClick={handleStartResearch}
                disabled={researchState.isResearching || !isConnected}
                className="w-full"
              >
                {researchState.isResearching ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Researching...
                  </>
                ) : (
                  <>
                    <Search className="w-4 h-4 mr-2" />
                    Start Research
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Progress Tab */}
        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Research Progress</CardTitle>
              {researchState.currentSupplement && (
                <p className="text-sm text-gray-600">
                  Researching: {researchState.currentSupplement}
                </p>
              )}
            </CardHeader>
            <CardContent>
              <div
                ref={progressRef}
                className="h-96 overflow-y-auto bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-2"
              >
                {researchState.progress.map((message, index) => (
                  <div key={index} className="text-sm">
                    {message}
                  </div>
                ))}
                {researchState.isResearching && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Processing...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Knowledge Graph Tab */}
        <TabsContent value="graph" className="space-y-4">
          {researchState.graphData ? (
            <SupplementGraph
              data={researchState.graphData}
              onNodeClick={(node) => {
                toast.info(`Selected: ${node.label}`);
              }}
            />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-96">
                <div className="text-center space-y-2">
                  <Brain className="w-12 h-12 mx-auto text-gray-400" />
                  <p className="text-gray-600">No graph data available</p>
                  <p className="text-sm text-gray-500">
                    Start a research to see the knowledge graph
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* AI Chat Tab */}
        <TabsContent value="chat" className="space-y-4">
          <GemmaChat
            onSendMessage={handleQueryGraph}
            context={{
              currentSupplement: researchState.currentSupplement,
              graphData: researchState.graphData,
              insights: researchState.insights
            }}
            disabled={!isConnected}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
