import { useState, useEffect, useRef, useCallback } from 'react';

interface AGUIMessage {
  type: 'query_response' | 'agent_event' | 'graph_state' | 'connection_established' | 'ping' | 'pong' | 'agent_completed' | 'agent_error' | 'start_supplement_research' | 'natural_language_query';
  payload?: any;
  agentId?: string;
  event?: any;
  data?: any;
  query?: string;
  response?: string;
}

interface UseAGUIClientReturn {
  isConnected: boolean;
  connectionStatus: string;
  lastMessage: AGUIMessage | null;
  sendMessage: (message: AGUIMessage) => void;
  connect: () => void;
  disconnect: () => void;
}

export const useAGUIClient = (): UseAGUIClientReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('Disconnected');
  const [lastMessage, setLastMessage] = useState<AGUIMessage | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const getWebSocketUrl = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

    // In development, backend runs on port 3001, frontend on 5173
    const host = process.env.NODE_ENV === 'development'
      ? 'localhost:3001'
      : window.location.host;

    return `${protocol}//${host}/agui/ws`;
  };

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionStatus('Connecting...');
    
    try {
      const ws = new WebSocket(getWebSocketUrl());
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('AG-UI WebSocket connected');
        setIsConnected(true);
        setConnectionStatus('Connected');
        reconnectAttempts.current = 0;
        
        // Send ping to verify connection
        ws.send(JSON.stringify({ type: 'ping' }));
      };

      ws.onmessage = (event) => {
        try {
          const message: AGUIMessage = JSON.parse(event.data);
          console.log('AG-UI message received:', message);
          setLastMessage(message);
          
          // Handle connection-specific messages
          if (message.type === 'connection_established') {
            console.log('AG-UI connection established:', message);
          } else if (message.type === 'pong') {
            console.log('AG-UI pong received');
          }
        } catch (error) {
          console.error('Error parsing AG-UI message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('AG-UI WebSocket closed:', event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus('Disconnected');
        wsRef.current = null;

        // Attempt to reconnect if not a manual close
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          setConnectionStatus(`Reconnecting in ${delay/1000}s...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        } else if (reconnectAttempts.current >= maxReconnectAttempts) {
          setConnectionStatus('Connection failed');
        }
      };

      ws.onerror = (error) => {
        console.error('AG-UI WebSocket error:', error);
        setConnectionStatus('Connection error');
      };

    } catch (error) {
      console.error('Error creating AG-UI WebSocket:', error);
      setConnectionStatus('Connection failed');
    }
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setConnectionStatus('Disconnected');
    reconnectAttempts.current = 0;
  }, []);

  const sendMessage = useCallback((message: AGUIMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        const messageString = JSON.stringify(message);
        console.log('Sending AG-UI message:', message);
        wsRef.current.send(messageString);
      } catch (error) {
        console.error('Error sending AG-UI message:', error);
      }
    } else {
      console.warn('AG-UI WebSocket not connected, cannot send message:', message);
    }
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  // Heartbeat to keep connection alive
  useEffect(() => {
    if (!isConnected) return;

    const heartbeatInterval = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        sendMessage({ type: 'ping' });
      }
    }, 30000); // Send ping every 30 seconds

    return () => clearInterval(heartbeatInterval);
  }, [isConnected, sendMessage]);

  // Handle visibility change to reconnect when tab becomes active
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isConnected) {
        console.log('Tab became visible, attempting to reconnect AG-UI...');
        connect();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isConnected, connect]);

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      console.log('Network came online, attempting to reconnect AG-UI...');
      if (!isConnected) {
        connect();
      }
    };

    const handleOffline = () => {
      console.log('Network went offline');
      setConnectionStatus('Offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isConnected, connect]);

  return {
    isConnected,
    connectionStatus,
    lastMessage,
    sendMessage,
    connect,
    disconnect
  };
};
